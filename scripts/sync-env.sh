#!/bin/bash

# Script to sync root .env file to frontend directory
# Vite requires the .env file to be in the frontend directory

ROOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
FRONTEND_DIR="$ROOT_DIR/frontend"

if [ -f "$ROOT_DIR/.env" ]; then
    echo "Copying .env from root to frontend directory..."
    cp "$ROOT_DIR/.env" "$FRONTEND_DIR/.env"
    echo "✅ Environment file synced successfully!"
else
    echo "❌ Root .env file not found. Please create one based on .env.example"
    exit 1
fi
