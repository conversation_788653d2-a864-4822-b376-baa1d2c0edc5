websockets-12.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
websockets-12.0.dist-info/LICENSE,sha256=PWoMBQ2L7FL6utUC5F-yW9ArytvXDeo01Ee2oP9Obag,1514
websockets-12.0.dist-info/METADATA,sha256=AxtN_yngdOy_wohEsaZLR4wyOo2SrG0R3a4ASgFWk0U,6612
websockets-12.0.dist-info/RECORD,,
websockets-12.0.dist-info/WHEEL,sha256=ReeDLt7JoWNv8uQs9jcofmiBOX-MvocIgD8kJPMfr7M,110
websockets-12.0.dist-info/top_level.txt,sha256=CMpdKklxKsvZgCgyltxUWOHibZXZ1uYIVpca9xsQ8Hk,11
websockets/__init__.py,sha256=Y4tu2IwlZ71xBCVGRaovGDBVdKH9dabTFyHMF3g-DgE,5658
websockets/__main__.py,sha256=8Dtga-XePHQ4jqgMMuXHT8XRH_hSvs8bEZ7-v49vTKg,4744
websockets/__pycache__/__init__.cpython-310.pyc,,
websockets/__pycache__/__main__.cpython-310.pyc,,
websockets/__pycache__/auth.cpython-310.pyc,,
websockets/__pycache__/client.cpython-310.pyc,,
websockets/__pycache__/connection.cpython-310.pyc,,
websockets/__pycache__/datastructures.cpython-310.pyc,,
websockets/__pycache__/exceptions.cpython-310.pyc,,
websockets/__pycache__/frames.cpython-310.pyc,,
websockets/__pycache__/headers.cpython-310.pyc,,
websockets/__pycache__/http.cpython-310.pyc,,
websockets/__pycache__/http11.cpython-310.pyc,,
websockets/__pycache__/imports.cpython-310.pyc,,
websockets/__pycache__/protocol.cpython-310.pyc,,
websockets/__pycache__/server.cpython-310.pyc,,
websockets/__pycache__/streams.cpython-310.pyc,,
websockets/__pycache__/typing.cpython-310.pyc,,
websockets/__pycache__/uri.cpython-310.pyc,,
websockets/__pycache__/utils.cpython-310.pyc,,
websockets/__pycache__/version.cpython-310.pyc,,
websockets/auth.py,sha256=pCeunT3V2AdwRt_Tpq9TrkdGY7qUlDHIEqeggj5yQFk,262
websockets/client.py,sha256=lIa508JN6xCQdzX3xfL3D6x8LpWyhct29Z64ScX1muQ,12562
websockets/connection.py,sha256=UivBmLaKmEOGpL1bU8uwh1PXIqMFiOUTVRi_gM7w5Rg,333
websockets/datastructures.py,sha256=6OgolFXa_rj9OOEtRnO2rXXzEB2A8aTd9GaTL1Yp66Q,5582
websockets/exceptions.py,sha256=DYWg9XEZ47WmWWC26117rfF5zes6zpBs-N4rZXkcn50,10296
websockets/extensions/__init__.py,sha256=QkZsxaJVllVSp1uhdD5uPGibdbx_091GrVVfS5LXcpw,98
websockets/extensions/__pycache__/__init__.cpython-310.pyc,,
websockets/extensions/__pycache__/base.cpython-310.pyc,,
websockets/extensions/__pycache__/permessage_deflate.cpython-310.pyc,,
websockets/extensions/base.py,sha256=5shEU7lqmsLC7-y3OCWih1VdS_wOImmZwuAaEKl9kDU,3271
websockets/extensions/permessage_deflate.py,sha256=bPFOAyTUDU7IIJyCGnWfr5yZF_J8dhCwJWt7jWuYM6c,24782
websockets/frames.py,sha256=wEmgahW6XwtGucEH4y7S93NWR3elfBvcQSNT3mzPOQw,13700
websockets/headers.py,sha256=RYryH2zqB_2Y02BTF2KsQFfYxAM6-Kh-A3Dv_32opAA,16120
websockets/http.py,sha256=T-c3LhgQHcdjKj2AJPzEJ6y_XK8tARDy_XjtmWqO8CM,897
websockets/http11.py,sha256=QcZ7u-UYbO98xQXrUz43qgaBXk-AyoQBHJBR0J9qYRE,12565
websockets/imports.py,sha256=SXXs0glid-UHcwla5yjR72DIbGeUTrS9VFagPvPvRNY,2790
websockets/legacy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
websockets/legacy/__pycache__/__init__.cpython-310.pyc,,
websockets/legacy/__pycache__/async_timeout.cpython-310.pyc,,
websockets/legacy/__pycache__/auth.cpython-310.pyc,,
websockets/legacy/__pycache__/client.cpython-310.pyc,,
websockets/legacy/__pycache__/compatibility.cpython-310.pyc,,
websockets/legacy/__pycache__/framing.cpython-310.pyc,,
websockets/legacy/__pycache__/handshake.cpython-310.pyc,,
websockets/legacy/__pycache__/http.cpython-310.pyc,,
websockets/legacy/__pycache__/protocol.cpython-310.pyc,,
websockets/legacy/__pycache__/server.cpython-310.pyc,,
websockets/legacy/async_timeout.py,sha256=nHW_nJYnxtuprwPduZMTl789KAymwmv0ukLbzm2Z8yU,8540
websockets/legacy/auth.py,sha256=WP68nZ1KAS0YCfNRyYG2M6LrNmT6xa430YnAjoeAP3g,6287
websockets/legacy/client.py,sha256=-tProFDSLOxIGhjo44klpFUKsrpYCpe6NJrk2qDzd-0,26123
websockets/legacy/compatibility.py,sha256=zgisuAUM4D5zKnyocY4JoNxhKjslodjOjGxu0QCbX1I,260
websockets/legacy/framing.py,sha256=M4J6ZPRK-zNqY_UgPQ4Qppc4R64aSMftO7FR_0VpG-Q,4998
websockets/legacy/handshake.py,sha256=RggPKl-w8oFJZQYZR0IdIOTrsz040pYp0Gu4L_D7_4U,5479
websockets/legacy/http.py,sha256=qmrM7pa0kuuJIroMVahBAH8_ZVqkD91YhwVux_xpfeI,6938
websockets/legacy/protocol.py,sha256=5H5uKIF7DbFgVCrwMd330GUH0ZWonihoi6QP27EN0Ww,63343
websockets/legacy/server.py,sha256=DwapgRDPgcCeZrC-2a1BxoNModWC0xbEk6ESLNcpReI,44737
websockets/protocol.py,sha256=bs5OH2UKrF1t7BQCpzU1DCzCJAHVNyLRzfnh8tcdbS0,24002
websockets/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
websockets/server.py,sha256=hJB3yl-QO2LLhPhyo_ahAI_dhJoxy9AB6S9vsyrpzuY,21136
websockets/speedups.c,sha256=ghPq-NF35VLVNkMv0uFDIruNpVISyW-qvoZgPpE65qw,5834
websockets/speedups.cpython-310-darwin.so,sha256=kLuYYqGzUbRvOewfnv2Vx3WTuFyzrWUYMbhOg9wrny4,51435
websockets/streams.py,sha256=8nv62HYyS74t_JSWGie4SoYAz8-jMcQacaHnD0RkK90,4038
websockets/sync/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
websockets/sync/__pycache__/__init__.cpython-310.pyc,,
websockets/sync/__pycache__/client.cpython-310.pyc,,
websockets/sync/__pycache__/connection.cpython-310.pyc,,
websockets/sync/__pycache__/messages.cpython-310.pyc,,
websockets/sync/__pycache__/server.cpython-310.pyc,,
websockets/sync/__pycache__/utils.cpython-310.pyc,,
websockets/sync/client.py,sha256=-9we3AHtE25pcT6EHGQ0oIyGzfYs18AzLpyDn4RLi94,11265
websockets/sync/connection.py,sha256=XBdZgDyZKyW3Bm_N5hhiT8uQvtj9lrB0qDVA4BP5wfA,29578
websockets/sync/messages.py,sha256=pTcWhwD-uwA0l4a26_xgPHgP8pjRYk5xrX5Vhq-JuCo,9484
websockets/sync/server.py,sha256=081Gzsneal-pFTvo5d7iPljUlQjV05ZNp4yVM57tfw0,18674
websockets/sync/utils.py,sha256=yUDxjeM4yVeXOZ_Go4ajgTUDOy-0rEWkjcR_RZDqcYY,1151
websockets/typing.py,sha256=naXTu4ToUF2k43XZhbW9Tt519yt876hVw66CjxQihII,1527
websockets/uri.py,sha256=oymYUo7bX8LofYzXpT3UqTZfkCt2y4s680Xr-qw88qk,3215
websockets/utils.py,sha256=QBhgbXn9ZvvLEzj-X8-zSHWVMkUqc6Wm-_HBjga5RNM,1150
websockets/version.py,sha256=XFPgNJrcyqeM0MPa7rBzmzplATL8PdK3-pv6hHkyBAw,2747
