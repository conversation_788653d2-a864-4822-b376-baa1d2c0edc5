postgrest-0.10.8.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
postgrest-0.10.8.dist-info/LICENSE,sha256=ht_XqL11RMhYK9-Ip9-6hpflU0qtpFOUO9VIdua-mVs,1077
postgrest-0.10.8.dist-info/METADATA,sha256=i3RwF-GSc-_OMlC_PCiALcYREcxsG9scHqsfy9a6NQQ,5082
postgrest-0.10.8.dist-info/RECORD,,
postgrest-0.10.8.dist-info/WHEEL,sha256=Zb28QaM1gQi8f4VCBhsUklF61CTlNYfs9YAZn-TOGFk,88
postgrest/__init__.py,sha256=tovWyiCk9FXQHxNQec79_MWWIaSE6yHdZt8PNz4Moic,742
postgrest/__pycache__/__init__.cpython-310.pyc,,
postgrest/__pycache__/base_client.cpython-310.pyc,,
postgrest/__pycache__/base_request_builder.cpython-310.pyc,,
postgrest/__pycache__/constants.cpython-310.pyc,,
postgrest/__pycache__/deprecated_client.cpython-310.pyc,,
postgrest/__pycache__/deprecated_get_request_builder.cpython-310.pyc,,
postgrest/__pycache__/exceptions.cpython-310.pyc,,
postgrest/__pycache__/types.cpython-310.pyc,,
postgrest/__pycache__/utils.cpython-310.pyc,,
postgrest/_async/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
postgrest/_async/__pycache__/__init__.cpython-310.pyc,,
postgrest/_async/__pycache__/client.cpython-310.pyc,,
postgrest/_async/__pycache__/request_builder.cpython-310.pyc,,
postgrest/_async/client.py,sha256=zCCb4eqPpdHrgOF7N_XXOMW8S7pkwbHHBhwBPqdlYas,3077
postgrest/_async/request_builder.py,sha256=vl9baH4JYwO28Wgu1T_LaT7b9GPTyTFQwOLP3hSSUXc,11429
postgrest/_sync/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
postgrest/_sync/__pycache__/__init__.cpython-310.pyc,,
postgrest/_sync/__pycache__/client.cpython-310.pyc,,
postgrest/_sync/__pycache__/request_builder.cpython-310.pyc,,
postgrest/_sync/client.py,sha256=8FVcce5JBy1ijU7Gv62ePp1Qe3ONcLdva9r2ZpwWcew,3025
postgrest/_sync/request_builder.py,sha256=W75JGOn2ecmhBE2j9Z7MK5sOzPCHqziyjrs-SO3ogPs,11360
postgrest/base_client.py,sha256=E3QHWwENwzawZVjYma0D8yxacyW5u9hcM_pIBZZnINY,1918
postgrest/base_request_builder.py,sha256=l0sHIYM3IOQBpiGDRlpAh3flWKpJgkGYBJolLnA9QW8,15948
postgrest/constants.py,sha256=9jR_zaLyfEvNI0WrL2vRs4NsjkGKomMk2sI_tjmcb3k,151
postgrest/deprecated_client.py,sha256=jcGEceLU_t_Fk57cZHijRLf3N2T10HGMTGDqAFS6IMg,409
postgrest/deprecated_get_request_builder.py,sha256=dWSHNtw6CXcp4HfjqkcSTaOeKeI007_wtMrHbDNODLU,422
postgrest/exceptions.py,sha256=T4ORME29C_CyIJUCWlDXoS3qSyv7LxMov1xLyMGvfho,1510
postgrest/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
postgrest/types.py,sha256=WjqZnDToAmbSTtxXtED17p-G2zCc-LtXNcc67umfKK0,874
postgrest/utils.py,sha256=jXgAnkqUXLj354c-G-3Jze8-eGeZvdXYM9mSI5_5uUE,551
