from flask import Blueprint, request, jsonify
from database import db
from auth import get_current_user, require_auth
from models import User
import logging

logger = logging.getLogger(__name__)
auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/me', methods=['GET'])
@require_auth
def get_me(user):
    """Get current user info"""
    return jsonify({
        'id': user.id,
        'email': user.email,
        'name': user.name,
        'avatar_url': user.avatar_url,
        'created_at': user.created_at.isoformat() if user.created_at else None
    })

@auth_bp.route('/login', methods=['POST'])
def login():
    """Handle user login/registration"""
    data = request.get_json()
    if not data or 'token' not in data:
        return jsonify({'error': 'Token required'}), 400
    
    # In a real app, you'd verify the token with Supabase
    # For now, we'll extract user info from the token payload
    import jwt
    try:
        payload = jwt.decode(data['token'], options={"verify_signature": False})
        user_id = payload.get('sub')
        email = payload.get('email')
        name = payload.get('user_metadata', {}).get('full_name')
        avatar_url = payload.get('user_metadata', {}).get('avatar_url')
        
        if not user_id or not email:
            return jsonify({'error': 'Invalid token'}), 400
        
        # Check if user exists, create if not
        print("Before")
        result = db.execute_query('users', 'select', filters={'id': user_id})
        print("After")
        
        if not result.data:
            # Create new user
            user_data = {
                'id': user_id,
                'email': email,
                'name': name,
                'avatar_url': avatar_url
            }
            db.execute_query('users', 'insert', data=user_data, admin=True)
        
        return jsonify({
            'message': 'Login successful',
            'user': {
                'id': user_id,
                'email': email,
                'name': name,
                'avatar_url': avatar_url
            }
        })
    
    except Exception as e:
        logger.error(f"Login error: {e}")
        return jsonify({'error': 'Login failed'}), 500

@auth_bp.route('/logout', methods=['POST'])
def logout():
    """Handle user logout"""
    return jsonify({'message': 'Logout successful'})
