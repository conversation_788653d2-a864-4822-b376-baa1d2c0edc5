from enum import Enum
from dataclasses import dataclass
from typing import Optional
from datetime import datetime

class UserRole(Enum):
    ADMIN = "admin"
    UPLOADER = "uploader"
    VIEWER = "viewer"

@dataclass
class User:
    id: str
    email: str
    name: Optional[str] = None
    avatar_url: Optional[str] = None
    created_at: Optional[datetime] = None

@dataclass
class Project:
    id: str
    name: str
    created_by: str
    description: Optional[str] = None
    created_at: Optional[datetime] = None

@dataclass
class ProjectMember:
    id: str
    project_id: str
    user_id: str
    role: UserRole
    added_at: Optional[datetime] = None

@dataclass
class Prompt:
    id: str
    project_id: str
    title: str
    content: str
    created_by: str
    created_at: Optional[datetime] = None

@dataclass
class File:
    id: str
    project_id: str
    name: str
    content: str
    uploaded_by: str
    uploaded_at: Optional[datetime] = None
    file_type: Optional[str] = None
