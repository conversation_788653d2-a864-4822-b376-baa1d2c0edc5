from flask import Flask, jsonify
from flask_cors import CORS
from config import Config
import logging

# Import route blueprints
from routes.auth_routes import auth_bp
from routes.project_routes import project_bp
from routes.member_routes import member_bp
from routes.prompt_routes import prompt_bp
from routes.file_routes import file_bp

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Disable CORS as requested
    # CORS(app, origins=["http://localhost:5173"])  # Commented out to disable CORS
    
    # Register blueprints
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(project_bp, url_prefix='/api/projects')
    app.register_blueprint(member_bp, url_prefix='/api')
    app.register_blueprint(prompt_bp, url_prefix='/api')
    app.register_blueprint(file_bp, url_prefix='/api')
    
    # Health check endpoint
    @app.route('/api/health')
    def health_check():
        return jsonify({'status': 'healthy', 'message': 'Note Sharer API is running'})
    
    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'error': 'Endpoint not found'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({'error': 'Internal server error'}), 500
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5001)
