from supabase import create_client, Client
from config import Config
import logging

logger = logging.getLogger(__name__)

class Database:
    def __init__(self):
        try:
            self.supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_KEY)
            self.admin_client: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
        except Exception as e:
            logger.warning(f"Failed to initialize Supabase clients: {e}")
            logger.warning("Using placeholder clients - update .env with real Supabase credentials")
            self.supabase = None
            self.admin_client = None
    
    def get_client(self, admin=False):
        """Get Supabase client (admin or regular)"""
        if not self.supabase or not self.admin_client:
            raise Exception("Supabase clients not initialized - check your .env configuration")
        return self.admin_client if admin else self.supabase

    def execute_query(self, table, operation, data=None, filters=None, admin=False):
        """Execute a database query with error handling"""
        try:
            client = self.get_client(admin)
            query = getattr(client.table(table), operation)

            if operation in ['insert', 'update', 'upsert'] and data:
                result = query(data)
            elif operation == 'select':
                result = query('*')
                if filters:
                    for key, value in filters.items():
                        result = result.eq(key, value)
            elif operation == 'delete' and filters:
                result = query()
                for key, value in filters.items():
                    result = result.eq(key, value)
            else:
                raise ValueError(f"Invalid operation: {operation}")

            return result.execute()
        except Exception as e:
            logger.error(f"Database query failed: {e}")
            raise

# Global database instance
db = Database()
