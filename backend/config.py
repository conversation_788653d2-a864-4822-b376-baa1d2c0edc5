import os
from dotenv import load_dotenv
from pathlib import Path

# Load .env from root directory
root_dir = Path(__file__).parent.parent
load_dotenv(root_dir / '.env')

class Config:
    SUPABASE_URL = os.getenv('SUPABASE_URL', 'https://placeholder.supabase.co')
    SUPABASE_KEY = os.getenv('SUPABASE_ANON_KEY', 'placeholder_anon_key')
    SUPABASE_SERVICE_KEY = os.getenv('SUPABASE_SERVICE_KEY', 'placeholder_service_key')
    SECRET_KEY = os.getenv('FLASK_SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = os.getenv('FLASK_ENV') == 'development'
    PORT = int(os.getenv('FLASK_PORT', 5001))
