import {
  Box,
  Container,
  <PERSON>ing,
  VStack,
  HStack,
  Button,
  Card,
  CardBody,
  Text,
  Badge,
  SimpleGrid,
  useDisclosure,
  useToast,
  Spinner,
  Center,
} from '@chakra-ui/react'
import { AddIcon } from '@chakra-ui/icons'
import { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { projectAPI, type Project } from '../lib/api'
import { CreateProjectModal } from '../components/projects/CreateProjectModal'
import { JoinProjectModal } from '../components/projects/JoinProjectModal'

export const DashboardPage = () => {
  const { user, loading: authLoading } = useAuth()
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const toast = useToast()
  
  const {
    isOpen: isCreateOpen,
    onOpen: onCreateOpen,
    onClose: onCreateClose,
  } = useDisclosure()
  
  const {
    isOpen: isJoinOpen,
    onOpen: onJoinOpen,
    onClose: onJoinClose,
  } = useDisclosure()

  useEffect(() => {
    if (user) {
      fetchProjects()
    }
  }, [user])

  const fetchProjects = async () => {
    try {
      const response = await projectAPI.getProjects()
      setProjects(response.data)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch projects',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    } finally {
      setLoading(false)
    }
  }

  const handleProjectCreated = (newProject: Project) => {
    setProjects([...projects, newProject])
    onCreateClose()
    toast({
      title: 'Success',
      description: 'Project created successfully',
      status: 'success',
      duration: 3000,
      isClosable: true,
    })
  }

  const handleProjectJoined = () => {
    fetchProjects() // Refresh the list
    onJoinClose()
    toast({
      title: 'Success',
      description: 'Joined project successfully',
      status: 'success',
      duration: 3000,
      isClosable: true,
    })
  }

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'red'
      case 'uploader':
        return 'blue'
      case 'viewer':
        return 'gray'
      default:
        return 'gray'
    }
  }

  if (authLoading || loading) {
    return (
      <Center h="50vh">
        <Spinner size="xl" color="blue.500" />
      </Center>
    )
  }

  return (
    <Container maxW="6xl" py={8}>
      <VStack spacing={8} align="stretch">
        <HStack justify="space-between">
          <Heading size="lg">My Projects</Heading>
          <HStack>
            <Button leftIcon={<AddIcon />} colorScheme="blue" onClick={onCreateOpen}>
              Create Project
            </Button>
            <Button variant="outline" onClick={onJoinOpen}>
              Join Project
            </Button>
          </HStack>
        </HStack>

        {projects.length === 0 ? (
          <Card>
            <CardBody textAlign="center" py={12}>
              <VStack spacing={4}>
                <Text fontSize="lg" color="gray.500">
                  No projects yet
                </Text>
                <Text color="gray.400">
                  Create a new project or join an existing one to get started
                </Text>
                <HStack>
                  <Button leftIcon={<AddIcon />} colorScheme="blue" onClick={onCreateOpen}>
                    Create Your First Project
                  </Button>
                  <Button variant="outline" onClick={onJoinOpen}>
                    Join Project
                  </Button>
                </HStack>
              </VStack>
            </CardBody>
          </Card>
        ) : (
          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
            {projects.map((project) => (
              <Card key={project.id} _hover={{ shadow: 'md' }} transition="all 0.2s">
                <CardBody>
                  <VStack align="start" spacing={3}>
                    <HStack justify="space-between" w="full">
                      <Heading size="md" noOfLines={1}>
                        {project.name}
                      </Heading>
                      <Badge colorScheme={getRoleBadgeColor(project.user_role || 'viewer')}>
                        {project.user_role}
                      </Badge>
                    </HStack>
                    
                    {project.description && (
                      <Text color="gray.600" noOfLines={2}>
                        {project.description}
                      </Text>
                    )}
                    
                    <Text fontSize="sm" color="gray.400">
                      Created {new Date(project.created_at || '').toLocaleDateString()}
                    </Text>
                    
                    <Button
                      as={Link}
                      to={`/projects/${project.id}`}
                      colorScheme="blue"
                      variant="outline"
                      size="sm"
                      w="full"
                    >
                      Open Project
                    </Button>
                  </VStack>
                </CardBody>
              </Card>
            ))}
          </SimpleGrid>
        )}
      </VStack>

      <CreateProjectModal
        isOpen={isCreateOpen}
        onClose={onCreateClose}
        onProjectCreated={handleProjectCreated}
      />
      
      <JoinProjectModal
        isOpen={isJoinOpen}
        onClose={onJoinClose}
        onProjectJoined={handleProjectJoined}
      />
    </Container>
  )
}
