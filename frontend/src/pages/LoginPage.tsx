import {
  Box,
  Button,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  useToast,
  Center,
  Flex,
  Icon,
  useColorModeValue,
  Divider,
  Stack,
  Circle,
  SimpleGrid,
  Badge,
} from '@chakra-ui/react'
import { FaGoogle, FaUsers, FaFileAlt, FaLightbulb, FaShieldAlt, FaRocket } from 'react-icons/fa'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { useEffect } from 'react'

const FeatureCard = ({ icon, title, description }: { icon: any; title: string; description: string }) => {
  const bg = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  return (
    <Box
      p={6}
      bg={bg}
      borderWidth={1}
      borderColor={borderColor}
      borderRadius="xl"
      shadow="sm"
      _hover={{ shadow: 'md', transform: 'translateY(-2px)' }}
      transition="all 0.2s"
    >
      <VStack spacing={3} align="start">
        <Circle size="12" bg="blue.50" color="blue.500">
          <Icon as={icon} boxSize={6} />
        </Circle>
        <Heading size="sm" color="gray.700">
          {title}
        </Heading>
        <Text fontSize="sm" color="gray.600" lineHeight="1.6">
          {description}
        </Text>
      </VStack>
    </Box>
  )
}

export const LoginPage = () => {
  const { signInWithGoogle, user, loading } = useAuth()
  const navigate = useNavigate()
  const toast = useToast()

  const bg = useColorModeValue('gray.50', 'gray.900')
  const cardBg = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  useEffect(() => {
    if (user) {
      navigate('/dashboard')
    }
  }, [user, navigate])

  const handleGoogleSignIn = async () => {
    try {
      await signInWithGoogle()
    } catch (error) {
      toast({
        title: 'Sign in failed',
        description: 'There was an error signing in with Google.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    }
  }

  if (loading) {
    return (
      <Center h="100vh" bg={bg}>
        <VStack spacing={4}>
          <Box
            w={12}
            h={12}
            border="4px solid"
            borderColor="blue.200"
            borderTopColor="blue.500"
            borderRadius="full"
            animation="spin 1s linear infinite"
          />
          <Text color="gray.600" fontWeight="medium">Loading...</Text>
        </VStack>
      </Center>
    )
  }

  return (
    <Box minH="100vh" bg={bg}>
      <Container maxW="6xl" py={20}>
        <Flex direction={{ base: 'column', lg: 'row' }} align="center" gap={16}>
          {/* Left Side - Sign In Form */}
          <VStack spacing={8} flex={1} maxW="md" w="full">
            <VStack spacing={6} textAlign="center">
              <Badge colorScheme="blue" px={3} py={1} borderRadius="full" fontSize="sm">
                Welcome Back
              </Badge>
              <Heading size="2xl" bgGradient="linear(to-r, blue.400, purple.500)" bgClip="text">
                Sign in to Note Sharer
              </Heading>
              <Text color="gray.600" fontSize="lg" lineHeight="1.6">
                Join thousands of teams collaborating seamlessly on notes, prompts, and files
              </Text>
            </VStack>

            <Box
              w="full"
              p={8}
              bg={cardBg}
              borderWidth={1}
              borderColor={borderColor}
              borderRadius="2xl"
              shadow="xl"
              position="relative"
              _before={{
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '4px',
                bgGradient: 'linear(to-r, blue.400, purple.500)',
                borderTopRadius: '2xl',
              }}
            >
              <VStack spacing={6}>
                <VStack spacing={2}>
                  <Heading size="lg" color="gray.700">
                    Get Started
                  </Heading>
                  <Text color="gray.500" fontSize="sm">
                    Sign in with your Google account to continue
                  </Text>
                </VStack>

                <Divider />

                <Button
                  leftIcon={<FaGoogle />}
                  colorScheme="red"
                  variant="outline"
                  size="lg"
                  w="full"
                  h={14}
                  fontSize="md"
                  fontWeight="semibold"
                  borderWidth={2}
                  _hover={{
                    transform: 'translateY(-1px)',
                    shadow: 'lg',
                    borderColor: 'red.300',
                  }}
                  _active={{
                    transform: 'translateY(0)',
                  }}
                  transition="all 0.2s"
                  onClick={handleGoogleSignIn}
                >
                  Continue with Google
                </Button>

                <HStack spacing={2} fontSize="xs" color="gray.400">
                  <Icon as={FaShieldAlt} />
                  <Text>Secure authentication powered by Google</Text>
                </HStack>
              </VStack>
            </Box>

            <Text fontSize="sm" color="gray.500" textAlign="center" maxW="sm">
              By signing in, you agree to our{' '}
              <Text as="span" color="blue.500" cursor="pointer" _hover={{ textDecoration: 'underline' }}>
                Terms of Service
              </Text>{' '}
              and{' '}
              <Text as="span" color="blue.500" cursor="pointer" _hover={{ textDecoration: 'underline' }}>
                Privacy Policy
              </Text>
            </Text>
          </VStack>

          {/* Right Side - Features */}
          <VStack spacing={8} flex={1} align="stretch">
            <VStack spacing={4} textAlign="center">
              <HStack spacing={2}>
                <Icon as={FaRocket} color="blue.500" boxSize={6} />
                <Heading size="lg" color="gray.700">
                  Why teams choose Note Sharer
                </Heading>
              </HStack>
              <Text color="gray.600" maxW="md">
                Everything you need for seamless collaboration in one powerful platform
              </Text>
            </VStack>

            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
              <FeatureCard
                icon={FaUsers}
                title="Team Collaboration"
                description="Invite team members with role-based permissions. Admins, uploaders, and viewers work together seamlessly."
              />
              <FeatureCard
                icon={FaLightbulb}
                title="Smart Prompts"
                description="Create and share AI prompts and instructions that your entire team can access and reuse."
              />
              <FeatureCard
                icon={FaFileAlt}
                title="Secure File Sharing"
                description="Upload and share text files with advanced access control and real-time collaboration features."
              />
              <FeatureCard
                icon={FaShieldAlt}
                title="Enterprise Security"
                description="Bank-level security with Google OAuth, role-based access, and encrypted data storage."
              />
            </SimpleGrid>

            <Box
              p={6}
              bg="blue.50"
              borderRadius="xl"
              borderLeft="4px solid"
              borderLeftColor="blue.400"
            >
              <HStack spacing={4}>
                <Circle size="12" bg="blue.100" color="blue.600">
                  <Text fontSize="xl" fontWeight="bold">✨</Text>
                </Circle>
                <VStack align="start" spacing={1}>
                  <Text fontWeight="semibold" color="blue.800">
                    Ready to get started?
                  </Text>
                  <Text fontSize="sm" color="blue.600">
                    Join thousands of teams already collaborating with Note Sharer
                  </Text>
                </VStack>
              </HStack>
            </Box>
          </VStack>
        </Flex>
      </Container>
    </Box>
  )
}
