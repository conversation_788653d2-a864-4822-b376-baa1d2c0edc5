import {
  Box,
  Button,
  Container,
  Heading,
  Text,
  VStack,
  useToast,
  Center,
  useColorModeValue,
  Icon,
} from '@chakra-ui/react'
import { FaGoogle } from 'react-icons/fa'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { useEffect } from 'react'

export const LoginPage = () => {
  const { signInWithGoogle, user, loading } = useAuth()
  const navigate = useNavigate()
  const toast = useToast()

  const bg = useColorModeValue('gray.50', 'gray.900')
  const cardBg = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  useEffect(() => {
    if (user) {
      navigate('/dashboard')
    }
  }, [user, navigate])

  const handleGoogleSignIn = async () => {
    try {
      await signInWithGoogle()
    } catch (error) {
      toast({
        title: 'Sign in failed',
        description: 'There was an error signing in with Google.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    }
  }

  if (loading) {
    return (
      <Center h="100vh" bg={bg}>
        <VStack spacing={4}>
          <Box
            w={12}
            h={12}
            border="4px solid"
            borderColor="blue.200"
            borderTopColor="blue.500"
            borderRadius="full"
            animation="spin 1s linear infinite"
          />
          <Text color="gray.600" fontWeight="medium">Loading...</Text>
        </VStack>
      </Center>
    )
  }

  return (
    <Box minH="100vh" bg={bg} position="relative" overflow="hidden">
      {/* Background decorative elements */}
      <Box
        position="absolute"
        top="-50%"
        left="-50%"
        w="200%"
        h="200%"
        bgGradient="radial(circle at 30% 20%, blue.100 0%, transparent 50%)"
        opacity={0.3}
        pointerEvents="none"
      />
      <Box
        position="absolute"
        bottom="-50%"
        right="-50%"
        w="200%"
        h="200%"
        bgGradient="radial(circle at 70% 80%, purple.100 0%, transparent 50%)"
        opacity={0.3}
        pointerEvents="none"
      />

      <Container maxW="md" py={20} position="relative" zIndex={1}>
        <Center minH="60vh">
          <VStack spacing={10} w="full">
            {/* Header */}
            <VStack spacing={6} textAlign="center">
              <Box>
                <Text
                  fontSize="4xl"
                  fontWeight="bold"
                  bgGradient="linear(to-r, blue.400, purple.500)"
                  bgClip="text"
                  letterSpacing="tight"
                >
                  Note Sharer
                </Text>
                <Box
                  w="60px"
                  h="4px"
                  bgGradient="linear(to-r, blue.400, purple.500)"
                  borderRadius="full"
                  mx="auto"
                  mt={2}
                />
              </Box>
              <VStack spacing={3}>
                <Heading size="xl" color="gray.700" fontWeight="semibold">
                  Welcome back
                </Heading>
                <Text color="gray.600" fontSize="lg" maxW="sm" lineHeight="1.6">
                  Sign in to continue collaborating with your team
                </Text>
              </VStack>
            </VStack>

            {/* Sign In Card */}
            <Box
              w="full"
              p={10}
              bg={cardBg}
              borderWidth={1}
              borderColor={borderColor}
              borderRadius="3xl"
              shadow="2xl"
              position="relative"
              backdropFilter="blur(10px)"
              _before={{
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '6px',
                bgGradient: 'linear(to-r, blue.400, purple.500)',
                borderTopRadius: '3xl',
              }}
            >
              <VStack spacing={8}>
                <VStack spacing={4} textAlign="center">
                  <Heading size="md" color="gray.700">
                    Sign in to your account
                  </Heading>
                  <Text color="gray.500" fontSize="sm">
                    Use your Google account to get started
                  </Text>
                </VStack>

                <Button
                  leftIcon={<FaGoogle />}
                  colorScheme="red"
                  variant="outline"
                  size="lg"
                  w="full"
                  h={16}
                  fontSize="md"
                  fontWeight="semibold"
                  borderWidth={2}
                  borderRadius="xl"
                  _hover={{
                    transform: 'translateY(-2px)',
                    shadow: 'xl',
                    borderColor: 'red.300',
                    bg: 'red.50',
                  }}
                  _active={{
                    transform: 'translateY(0)',
                  }}
                  transition="all 0.3s ease"
                  onClick={handleGoogleSignIn}
                >
                  Continue with Google
                </Button>

                <Text fontSize="xs" color="gray.400" textAlign="center">
                  <Icon as={FaGoogle} mr={2} />
                  Secure authentication powered by Google
                </Text>
              </VStack>
            </Box>

            {/* Footer */}
            <Text fontSize="sm" color="gray.500" textAlign="center" maxW="sm">
              By signing in, you agree to our{' '}
              <Text
                as="span"
                color="blue.500"
                cursor="pointer"
                fontWeight="medium"
                _hover={{ textDecoration: 'underline' }}
              >
                Terms of Service
              </Text>{' '}
              and{' '}
              <Text
                as="span"
                color="blue.500"
                cursor="pointer"
                fontWeight="medium"
                _hover={{ textDecoration: 'underline' }}
              >
                Privacy Policy
              </Text>
            </Text>
          </VStack>
        </Center>
      </Container>
    </Box>
  )
}
