import React, { createContext, useContext, useEffect, useState } from 'react'
import { supabase } from '../lib/supabase'
import { authAPI, type User } from '../lib/api'

interface AuthContextType {
  user: User | null
  loading: boolean
  signInWithGoogle: () => Promise<void>
  signOut: () => Promise<void>
  isAuthenticated: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Check if user is already logged in
    const token = localStorage.getItem('supabase_token')
    if (token) {
      handleLogin(token)
    } else {
      setLoading(false)
    }

    // Listen for auth changes from Supabase (only for OAuth flow)
    try {
      const {
        data: { subscription },
      } = supabase.auth.onAuthStateChange(async (event, session) => {
        if (session?.access_token) {
          localStorage.setItem('supabase_token', session.access_token)
          await handleLogin(session.access_token)
        } else if (event === 'SIGNED_OUT') {
          localStorage.removeItem('supabase_token')
          setUser(null)
          setLoading(false)
        }
      })

      return () => subscription.unsubscribe()
    } catch (error) {
      console.warn('Supabase auth setup failed:', error)
      setLoading(false)
    }
  }, [])

  const handleLogin = async (token: string) => {
    try {
      // Send token to Flask backend for validation and user creation/retrieval
      await authAPI.login(token)
      const response = await authAPI.getMe()
      setUser(response.data)
    } catch (error) {
      console.error('Login error:', error)
      localStorage.removeItem('supabase_token')
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  const signInWithGoogle = async () => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`
        }
      })
      if (error) {
        console.error('Error signing in with Google:', error)
        throw error
      }
    } catch (error) {
      console.error('Supabase OAuth error:', error)
      throw new Error('Authentication service unavailable. Please check Supabase configuration.')
    }
  }

  const signOut = async () => {
    try {
      // Notify Flask backend
      await authAPI.logout()
    } catch (error) {
      console.error('Logout API error:', error)
    }

    try {
      // Sign out from Supabase auth
      const { error } = await supabase.auth.signOut()
      if (error) {
        console.error('Error signing out:', error)
      }
    } catch (error) {
      console.error('Supabase signout error:', error)
    }

    localStorage.removeItem('supabase_token')
    setUser(null)
  }

  const value = {
    user,
    loading,
    signInWithGoogle,
    signOut,
    isAuthenticated: !!user,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
