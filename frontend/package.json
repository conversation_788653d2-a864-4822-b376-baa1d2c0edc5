{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^3.21.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@tanstack/react-query": "^5.80.7", "axios": "^1.10.0", "framer-motion": "^12.18.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}