# Notes
- <PERSON><PERSON> will use <PERSON><PERSON> (with <PERSON><PERSON> UI) for the frontend.
- Frontend will use Flask API client for all backend operations.
- Backend will be Flask (Python) with Supabase as the database.
- Authentication and user/project management logic will move to Flask backend.
- Users can create/join projects.
- Projects have roles: admin, uploader, viewer.
- Dashboard has tabs: People, Prompts, Files.
- Role-based permissions: Admin (edit), Uploader (view/upload), Viewer (view only).
- Files are uploaded as text.
- Frontend will be bootstrapped with Vite (React + Chakra UI), not Next.js.
- Supabase client set up in `src/lib/supabase.ts` with placeholder credentials.
- `.env.local.example` will be created for environment variables. Replace with your own credentials later.
- Initial layout components (`Navbar`, `Layout`) created in `src/components/layout/`.
- Auth callback route, middleware, Navbar auth state, and sign out implemented.
- Utility types and functions added for Supabase and general use.
- CORS will be disabled in Flask backend as requested.

## Task List
- [x] Initialize Next.js project and connect to Vercel
- [x] Set up Supabase project and connect to Next.js (with placeholder credentials)
  - [x] Install Supabase packages
  - [x] Create Supabase client in `src/lib/supabase.ts`
  - [x] Add `.env.local.example` for environment variables
- [x] Implement Google authentication with Supabase Auth
  - [x] Create authentication callback route
  - [x] Add authentication middleware
  - [x] Update Navbar to handle auth state
  - [x] Add sign out functionality
  - [x] Add Supabase database types and auth utility functions
- [x] Create database schema for users, projects, roles, prompts, files
- [ ] Set up Flask backend project
  - [x] Connect Flask to Supabase database (PostgREST or direct Postgres client)
  - [x] Implement REST API endpoints for auth, projects, roles, prompts, files
  - [x] Secure endpoints and implement role-based access control in Flask
  - [x] Disable CORS in Flask backend
- [ ] Convert frontend to React + Chakra UI
  - [x] Remove Next.js/Tailwind dependencies
  - [x] Set up Chakra UI provider and theme
  - [x] Implement Flask API client in frontend
  - [ ] Refactor authentication flow to use Flask API
  - [ ] Refactor project creation/join and dashboard to use Flask API
  - [ ] Test all flows end-to-end
- [ ] Update documentation for new architecture
- [ ] Build project creation/join flow
- [ ] Implement project dashboard with tabs:
  - [ ] People tab (add/manage users & roles)
  - [ ] Prompts tab (add/remove instructions)
  - [ ] Files tab (upload/view files, role-based access)
- [ ] Set up role-based access control in frontend and backend
- [ ] Test end-to-end flow (login, create/join project, dashboard, permissions)
- [ ] Implement dashboard layout and sidebar
- [ ] Add loading spinner and utility functions

## Current Goal
Convert frontend to React + Chakra UI and integrate Flask API
